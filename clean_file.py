import re

def clean_file(input_path, output_path):
    """清理文件中的不可见Unicode字符"""
    try:
        # 读取原文件
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除不可见的Unicode字符（零宽字符等）
        # 保留基本的ASCII字符和常见的中文字符
        cleaned_content = re.sub(r'[\u200B-\u200D\uFEFF]', '', content)
        
        # 移除行首行尾的空白字符
        lines = cleaned_content.split('\n')
        cleaned_lines = [line.strip() for line in lines if line.strip()]
        
        # 写入清理后的文件
        with open(output_path, 'w', encoding='utf-8') as f:
            for line in cleaned_lines:
                f.write(line + '\n')
        
        print(f"文件清理完成！")
        print(f"原文件行数: {len(content.split('\n'))}")
        print(f"清理后行数: {len(cleaned_lines)}")
        print(f"清理后的文件保存为: {output_path}")
        
    except Exception as e:
        print(f"清理过程中出现错误: {e}")

if __name__ == "__main__":
    input_file = r"c:\Users\<USER>\Downloads\荒.txt"
    output_file = r"c:\Users\<USER>\Downloads\荒_cleaned.txt"
    clean_file(input_file, output_file) 