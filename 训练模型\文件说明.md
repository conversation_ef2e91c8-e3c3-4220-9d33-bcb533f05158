# 📁 3T→7T跨场强DeepCEST训练系统文件说明

## 🎯 **核心文件（按使用顺序）**

### **第1步：整理拟合数据**
📄 `步骤1_整理拟合数据_organize_fitted_data.m`
- 功能：从拟合结果文件夹复制MAT文件到训练文件夹
- 保持：原始患者ID和检查时间结构
- 输出：Data/患者ID/检查时间/拟合数据.mat

### **第2步：批量处理数据**
📄 `步骤2_批量处理数据_batch_process_organized_data.m`
- 功能：合并所有患者数据并生成7T标签（内置物理模型）
- 处理：数据质量控制和7T参数生成
- 输出：DeepCEST_5Pool_OrganizedData_TrainData.mat (3T输入+7T标签)

### **第3步：训练神经网络**
📄 `步骤3_训练模型_DeepCEST_training_5pool.m`
- 功能：训练150×150×150的三层神经网络
- 目标：3T→7T跨场强预测模型（15参数）
- 输出：Networks/CESTNet_3T_to_7T_*.mat

### **第4步：模型推理测试**
📄 `步骤4_模型推理_DeepCEST_5pool_inference.m`
- 功能：使用训练好的网络进行3T→7T参数预测
- **新增**：自动数据格式检测和转换（2D向量 ↔ 4D图像）
- 支持：2D向量格式（拟合脚本输出）和4D图像格式（标准CEST）
- 生成：15个7T质量的参数图
- 输出：Results/DeepCEST_3T_to_7T_Results.mat + 15个DCM文件

## 📖 **使用指南**
📄 `使用指南_中文版.md`
- 详细的中文使用说明
- 故障排除指南
- 参数配置说明

📄 `数据格式转换说明.md` ⭐ **新增**
- 2D向量 ↔ 4D图像格式转换详解
- 自动格式检测功能说明
- 转换质量控制和故障排除

## 🚀 **快速开始**

```matlab
% 在MATLAB中按顺序执行：
cd('C:\Users\<USER>\Documents\augment-projects\1\训练模型')

run('步骤1_整理拟合数据_organize_fitted_data.m')
run('步骤2_批量处理数据_batch_process_organized_data.m')
run('步骤3_训练模型_DeepCEST_training_5pool.m')
run('步骤4_模型推理_DeepCEST_5pool_inference.m')
```

## 📊 **生成的文件夹结构**

```
训练模型/
├── Data/                                    # 整理后的数据
│   └── 患者ID/检查时间/拟合数据.mat
├── Networks/                                # 训练好的网络
│   └── CESTNet_3T_to_7T_*.mat
├── Results/                                 # 推理结果
│   ├── DeepCEST_3T_to_7T_Results.mat       # MAT格式结果
│   └── DCM_ParameterMaps/                   # DCM格式参数图 ⭐
│       ├── WaterAmplitude.dcm               # 水池振幅
│       ├── MTRAmplitude.dcm                 # MTR振幅
│       ├── APTAmplitude.dcm                 # APT振幅
│       ├── NOEAmplitude.dcm                 # NOE振幅
│       ├── CrAmplitude.dcm                  # Cr振幅
│       ├── ... (积分和位置参数)
│       └── ParameterMap_Summary.txt         # 参数汇总信息
└── DeepCEST_5Pool_OrganizedData_TrainData.mat  # 合并训练数据
```

## ✨ **系统特点**

- 🧲 **跨场强预测** - 从3T数据预测7T质量参数
- 🇨🇳 **完全中文化** - 所有界面和注释都是中文
- 📝 **分步骤操作** - 1234步骤，清晰明了
- 🧪 **物理模型驱动** - 基于CEST物理原理生成7T标签
- 🔄 **自动化处理** - 一键完成复杂操作
- 🔍 **智能格式检测** - 自动识别2D向量或4D图像格式
- 🔄 **无缝格式转换** - 2D↔4D数据格式自动转换
- 📊 **详细反馈** - 每步都有进度提示
- 🛠️ **可定制配置** - 支持参数调整

**现在您可以用3T设备获得7T质量的CEST参数了！** 🚀🧲
