%% 步骤2：批量处理组织化数据 - 合并所有患者数据用于训练
% Step 2: Batch Process Organized Data - Combine All Patient Data for Training
%
% 功能说明：
% 1. 加载所有整理好的患者拟合数据
% 2. 进行数据质量控制和清理
% 3. 合并为单一的训练数据集
% 4. 保留患者和检查时间追踪信息
%
% 使用方法：
% cd('C:\Users\<USER>\Documents\augment-projects\1\训练模型')
% run('步骤2_批量处理数据_batch_process_organized_data.m')

clear all; close all; clc; warning off;

fprintf('=== 步骤2：批量处理组织化数据 ===\n');
fprintf('=== Step 2: Batch Process Organized Data ===\n\n');

%% 第1部分：加载组织信息 - Load Organization Info
fprintf('>> 第1部分：加载数据组织信息\n');

data_base = 'Data';  % 数据文件夹 - Data folder
org_info_file = fullfile(data_base, 'data_organization_info.mat');

% 尝试加载组织信息文件
% Try to load organization info file
if exist(org_info_file, 'file')
    load(org_info_file);
    fprintf('   ✅ 已加载组织信息: 找到 %d 个文件\n', copied_count);
    fprintf('   ✅ Loaded organization info: %d files found\n', copied_count);
else
    fprintf('   ⚠️  没有找到组织信息，正在扫描目录...\n');
    fprintf('   ⚠️  No organization info found. Scanning directory...\n');
    
    % 扫描Data目录
    % Scan the Data directory
    patient_info = [];
    copied_count = 0;
    
    if exist(data_base, 'dir')
        % 查找患者文件夹
        % Find patient directories
        patient_dirs = dir(data_base);
        patient_dirs = patient_dirs([patient_dirs.isdir] & ~ismember({patient_dirs.name}, {'.', '..'}));
        
        for p = 1:length(patient_dirs)
            patient_id = patient_dirs(p).name;  % 患者ID
            patient_dir = fullfile(data_base, patient_id);
            
            % 查找检查时间文件夹
            % Find session directories
            session_dirs = dir(patient_dir);
            session_dirs = session_dirs([session_dirs.isdir] & ~ismember({session_dirs.name}, {'.', '..'}));
            
            for s = 1:length(session_dirs)
                session_id = session_dirs(s).name;  % 检查时间ID
                session_dir = fullfile(patient_dir, session_id);
                
                % 查找MAT文件
                % Find MAT files
                mat_files = dir(fullfile(session_dir, '*.mat'));
                for m = 1:length(mat_files)
                    copied_count = copied_count + 1;
                    patient_info(copied_count).patient_id = patient_id;
                    patient_info(copied_count).session_id = session_id;
                    patient_info(copied_count).filename = mat_files(m).name;
                    patient_info(copied_count).target_path = fullfile(session_dir, mat_files(m).name);
                end
            end
        end
        
        fprintf('   扫描完成: 找到 %d 个文件\n', copied_count);
        fprintf('   Scan complete: found %d files\n', copied_count);
    else
        error('❌ 数据文件夹不存在。请先运行步骤1！\nData directory not found. Please run Step 1 first!');
    end
end

if copied_count == 0
    error('❌ 没有找到拟合数据。请先运行步骤1！\nNo fitted data found. Please run Step 1 first!');
end

%% 第2部分：加载频率信息 - Load Frequency Information
fprintf('\n>> 第2部分：加载频率信息\n');

% 频率文件路径
% Frequency file path
freq_file = 'C:\Users\<USER>\Documents\augment-projects\DeepCEST_PengZiYang\Scripts\CestToolBox\CEST_freq_48points.txt';

if exist(freq_file, 'file')
    FreqList = importdata(freq_file);           % 导入频率列表
    ppm_offsets = FreqList / 128;               % 转换为ppm偏移（基于3T采集）
    offs_hz = ppm_offsets * 298.1 * 1e6;       % 转换为Hz偏移（7T场强预测目标）

    fprintf('   ✅ 已加载频率信息: %d 个频率点\n', length(FreqList));
    fprintf('   ✅ Loaded frequency information: %d points\n', length(FreqList));
    fprintf('   输入频率范围(3T采集): %.2f 到 %.2f ppm\n', min(ppm_offsets), max(ppm_offsets));
    fprintf('   Input frequency range (3T acquisition): %.2f to %.2f ppm\n', min(ppm_offsets), max(ppm_offsets));
    fprintf('   目标频率范围(7T预测): %.1f 到 %.1f Hz\n', min(offs_hz), max(offs_hz));
    fprintf('   Target frequency range (7T prediction): %.1f to %.1f Hz\n', min(offs_hz), max(offs_hz));
else
    error('❌ 频率文件不存在: %s\nFrequency file not found: %s', freq_file);
end

%% 第3部分：处理每个拟合数据文件 - Process Each Fitted Data File
fprintf('\n>> 第3部分：逐个处理拟合数据文件\n');

% 初始化合并数据存储
% Initialize combined data storage
all_zInput = [];                % 所有Z谱数据 - All Z-spectra data
all_pTarget = [];               % 所有参数数据 - All parameter data
all_patient_labels = [];        % 患者标签 - Patient labels
all_session_labels = [];        % 检查时间标签 - Session labels
patient_stats = struct();       % 患者统计信息 - Patient statistics
successful_count = 0;           % 成功处理计数 - Successful processing count

for i = 1:copied_count
    patient_id = patient_info(i).patient_id;    % 患者ID
    session_id = patient_info(i).session_id;    % 检查时间ID
    mat_file = patient_info(i).target_path;     % MAT文件路径
    
    fprintf('\n--- 正在处理 %d/%d: %s/%s ---\n', i, copied_count, patient_id, session_id);
    fprintf('--- Processing %d/%d: %s/%s ---\n', i, copied_count, patient_id, session_id);
    
    try
        % 加载拟合数据
        % Load the fitted data
        fprintf('   正在加载: %s\n', mat_file);
        fprintf('   Loading: %s\n', mat_file);
        data = load(mat_file);
        
        % 检查必需的变量
        % Check required variables
        if ~isfield(data, 'zInput') || ~isfield(data, 'pTarget')
            fprintf('   ❌ 缺少必需变量 (zInput, pTarget)\n');
            fprintf('   ❌ Missing required variables (zInput, pTarget)\n');
            continue;
        end
        
        zInput_patient = data.zInput;    % 患者Z谱数据
        pTarget_patient = data.pTarget;  % 患者参数数据
        
        % 验证数据维度
        % Validate dimensions
        if size(zInput_patient, 1) ~= length(FreqList)
            fprintf('   ⚠️  频率点不匹配: 期望 %d, 实际 %d\n', length(FreqList), size(zInput_patient, 1));
            fprintf('   ⚠️  Frequency mismatch: expected %d, got %d\n', length(FreqList), size(zInput_patient, 1));
        end
        
        if size(pTarget_patient, 1) ~= 15
            fprintf('   ⚠️  参数数量不匹配: 期望 15, 实际 %d\n', size(pTarget_patient, 1));
            fprintf('   ⚠️  Parameter mismatch: expected 15, got %d\n', size(pTarget_patient, 1));
        end
        
        n_voxels = size(zInput_patient, 2);  % 像素数量
        fprintf('   数据维度: zInput %s, pTarget %s\n', ...
                mat2str(size(zInput_patient)), mat2str(size(pTarget_patient)));
        fprintf('   Data dimensions: zInput %s, pTarget %s\n', ...
                mat2str(size(zInput_patient)), mat2str(size(pTarget_patient)));
        
        %% 数据质量控制 - Quality Control
        fprintf('   正在进行质量控制...\n');
        fprintf('   Quality control...\n');
        
        valid_voxels = true(n_voxels, 1);  % 有效像素标记
        
        for v = 1:n_voxels
            z_spectrum = zInput_patient(:, v);  % 单个像素的Z谱
            
            % 基本质量检查
            % Basic quality checks
            
            % 检查异常值
            % Check for unrealistic values
            if any(z_spectrum <= 0) || any(z_spectrum > 1.5) || any(isnan(z_spectrum))
                valid_voxels(v) = false;
                continue;
            end
            
            % 检查平坦谱线
            % Check for flat spectra
            if std(z_spectrum) < 0.01
                valid_voxels(v) = false;
                continue;
            end
            
            % 检查基线
            % Check baseline
            baseline_indices = abs(ppm_offsets) > 5;  % 远离共振的频率点
            if any(baseline_indices)
                baseline_mean = mean(z_spectrum(baseline_indices));
                if baseline_mean < 0.7 || baseline_mean > 1.2
                    valid_voxels(v) = false;
                end
            end
        end
        
        % 保留有效像素
        % Keep only valid voxels
        zInput_clean = zInput_patient(:, valid_voxels);
        pTarget_clean = pTarget_patient(:, valid_voxels);
        n_valid = sum(valid_voxels);
        
        fprintf('   有效像素: %d/%d (%.1f%%)\n', n_valid, n_voxels, 100*n_valid/n_voxels);
        fprintf('   Valid voxels: %d/%d (%.1f%%)\n', n_valid, n_voxels, 100*n_valid/n_voxels);
        
        % 检查最小像素数阈值
        % Check minimum threshold
        if n_valid < 500  % 最小阈值
            fprintf('   ❌ 有效像素太少，跳过\n');
            fprintf('   ❌ Too few valid voxels, skipping\n');
            continue;
        end
        
        %% 生成7T标签数据 - Generate 7T Label Data
        fprintf('   正在生成7T标签数据...\n');
        fprintf('   Generating 7T label data...\n');

        % 从3T参数生成7T标签 (内置物理模型)
        % Generate 7T labels from 3T parameters (built-in physics model)
        pTarget_7T = generate_7T_labels_internal(pTarget_clean, ppm_offsets);

        %% 添加到合并数据集 - Add to Combined Dataset
        all_zInput = [all_zInput, zInput_clean];      % 合并Z谱数据（3T输入）
        all_pTarget = [all_pTarget, pTarget_7T];      % 合并7T标签数据
        
        % 创建患者-检查时间组合的唯一标签
        % Create unique labels for patient-session combinations
        successful_count = successful_count + 1;
        all_patient_labels = [all_patient_labels, repmat(successful_count, 1, n_valid)];
        
        % 单独存储检查时间信息
        % Store session info separately
        session_info = sprintf('%s_%s', patient_id, session_id);  % 组合ID
        all_session_labels = [all_session_labels, repmat(successful_count, 1, n_valid)];
        
        % 存储统计信息
        % Store statistics
        patient_stats(successful_count).patient_id = patient_id;        % 患者ID
        patient_stats(successful_count).session_id = session_id;        % 检查时间ID
        patient_stats(successful_count).combined_id = session_info;     % 组合ID
        patient_stats(successful_count).n_voxels = n_valid;             % 有效像素数
        patient_stats(successful_count).data_quality = mean(std(zInput_clean, 0, 1));  % 数据质量指标
        patient_stats(successful_count).param_ranges = [min(pTarget_clean, [], 2), max(pTarget_clean, [], 2)];  % 参数范围
        
        fprintf('   ✅ 已添加到训练集: %d 个像素\n', n_valid);
        fprintf('   ✅ Added to training set: %d voxels\n', n_valid);
        
    catch ME
        fprintf('   ❌ 处理 %s/%s 时出错: %s\n', patient_id, session_id, ME.message);
        fprintf('   ❌ Error processing %s/%s: %s\n', patient_id, session_id, ME.message);
        continue;
    end
end

%% 第4部分：最终化合并数据集 - Finalize Combined Dataset
fprintf('\n=== 第4部分：最终化合并数据集 ===\n');
fprintf('=== Part 4: Finalize Combined Dataset ===\n');

if isempty(all_zInput)
    error('❌ 没有成功处理任何有效数据\nNo valid data was processed successfully');
end

% 准备最终训练变量
% Prepare final variables for training
zInput = all_zInput;    % Z谱输入数据
pTarget = all_pTarget;  % 参数目标数据
offs = offs_hz';        % 频率偏移（列向量）

% 参数名称
% Parameter names
param_names = {
    'Water Amplitude', 'MTR Amplitude', 'APT Amplitude', 'NOE Amplitude', 'Cr Amplitude', ...
    'Water Integral', 'MTR Integral', 'APT Integral', 'NOE Integral', 'Cr Integral', ...
    'Water Position', 'MTR Position', 'APT Position', 'NOE Position', 'Cr Position'
};

% 创建患者ID列表
% Create patient IDs list
patient_ids = {patient_stats.combined_id};

fprintf('最终合并数据集:\n');
fprintf('Final combined dataset:\n');
fprintf('  成功的患者-检查时间: %d\n', successful_count);
fprintf('  Successful patient-sessions: %d\n', successful_count);
fprintf('  总像素数: %d\n', size(zInput, 2));
fprintf('  Total voxels: %d\n', size(zInput, 2));
fprintf('  频率点数: %d\n', size(zInput, 1));
fprintf('  Frequency points: %d\n', size(zInput, 1));
fprintf('  参数数: %d\n', size(pTarget, 1));
fprintf('  Parameters: %d\n', size(pTarget, 1));

%% 第5部分：数据增强（如需要）- Data Augmentation (if needed)
if size(zInput, 2) < 20000
    fprintf('\n=== 第5部分：应用数据增强 ===\n');
    fprintf('=== Part 5: Applying Data Augmentation ===\n');
    
    zInput_original = zInput;    % 保存原始数据
    pTarget_original = pTarget;  % 保存原始参数
    
    % 不同噪声水平
    % Different noise levels
    noise_levels = [0.005, 0.01];  % 0.5% 和 1% 噪声
    for noise_level = noise_levels
        fprintf('   正在添加 %.1f%% 噪声增强...\n', noise_level*100);
        fprintf('   Adding %.1f%% noise augmentation...\n', noise_level*100);
        
        % 添加高斯噪声
        % Add Gaussian noise
        zInput_noisy = zInput_original + noise_level * randn(size(zInput_original));
        zInput_noisy = max(0, min(1.5, zInput_noisy));  % 限制在合理范围内
        
        % 合并到数据集
        % Combine to dataset
        zInput = [zInput, zInput_noisy];
        pTarget = [pTarget, pTarget_original];
    end
    
    fprintf('   增强后数据集大小: %d 个样本\n', size(zInput, 2));
    fprintf('   Augmented dataset size: %d samples\n', size(zInput, 2));
end

%% 第6部分：保存合并训练数据 - Save Combined Training Data
fprintf('\n=== 第6部分：保存合并训练数据 ===\n');
fprintf('=== Part 6: Save Combined Training Data ===\n');

% 保存文件名
% Save filename
save_filename = 'DeepCEST_5Pool_OrganizedData_TrainData.mat';

% 保存所有训练相关变量
% Save all training-related variables
save(save_filename, 'zInput', 'pTarget', 'offs', 'param_names', ...
     'ppm_offsets', 'all_patient_labels', 'all_session_labels', 'patient_stats', ...
     'patient_ids', 'FreqList', 'successful_count');

fprintf('   ✅ 合并训练数据已保存到: %s\n', save_filename);
fprintf('   ✅ Combined training data saved to: %s\n', save_filename);

%% 第7部分：显示最终统计 - Display Final Statistics
fprintf('\n=== 第7部分：最终数据集统计 ===\n');
fprintf('=== Part 7: Final Dataset Statistics ===\n');

fprintf('患者-检查时间分布:\n');
fprintf('Patient-Session breakdown:\n');
for i = 1:successful_count
    fprintf('  %s: %d 像素 (质量: %.3f)\n', ...
            patient_stats(i).combined_id, patient_stats(i).n_voxels, ...
            patient_stats(i).data_quality);
end

fprintf('\n参数统计:\n');
fprintf('Parameter statistics:\n');
for p = 1:min(15, size(pTarget, 1))
    fprintf('参数 %2d (%s):\n', p, param_names{p});
    fprintf('  范围: [%.3f, %.3f]\n', min(pTarget(p,:)), max(pTarget(p,:)));
    fprintf('  均值 ± 标准差: %.3f ± %.3f\n', mean(pTarget(p,:)), std(pTarget(p,:)));
end

%% 第8部分：创建可视化 - Create Visualization
fprintf('\n=== 第8部分：创建数据可视化 ===\n');
fprintf('=== Part 8: Create Visualization ===\n');

figure('Position', [100, 100, 1400, 800]);

% 不同患者-检查时间的样本Z谱
% Sample Z-spectra by patient-session
subplot(2, 3, 1);
colors = lines(min(10, successful_count));  % 颜色映射
hold on;
for i = 1:min(10, successful_count)
    patient_voxels = find(all_patient_labels == i);  % 找到该患者的像素
    if ~isempty(patient_voxels)
        sample_idx = patient_voxels(1);  % 取第一个像素作为样本
        plot(ppm_offsets, zInput(:, sample_idx), 'Color', colors(i,:), ...
             'LineWidth', 1.5, 'DisplayName', patient_stats(i).combined_id);
    end
end
xlabel('频率偏移 (ppm) / Frequency Offset (ppm)');
ylabel('Z谱强度 / Z-spectrum Intensity');
title('不同患者-检查时间的样本Z谱 / Sample Z-spectra by Patient-Session');
legend('show', 'Location', 'best');
grid on;

% 患者-检查时间像素分布
% Patient-session voxel distribution
subplot(2, 3, 2);
voxel_counts = [patient_stats.n_voxels];  % 每个患者-检查时间的像素数
bar(voxel_counts);
xlabel('患者-检查时间索引 / Patient-Session Index');
ylabel('像素数量 / Number of Voxels');
title('每个患者-检查时间的像素数 / Voxels per Patient-Session');
grid on;

% 关键参数分布
% Parameter distributions
key_params = [1, 3, 4, 2];  % Water, APT, NOE, MTR 振幅
key_names = {'水池振幅/Water Amplitude', 'APT振幅/APT Amplitude', 'NOE振幅/NOE Amplitude', 'MTR振幅/MTR Amplitude'};

for i = 1:4
    subplot(2, 3, i+2);
    if key_params(i) <= size(pTarget, 1)
        histogram(pTarget(key_params(i), :), 50);  % 50个直方图箱
        xlabel(key_names{i});
        ylabel('计数 / Count');
        title(sprintf('%s 分布', key_names{i}));
        grid on;
    end
end

sgtitle('组织化多患者CEST训练数据概览 / Organized Multi-Patient CEST Training Data Overview');

fprintf('   ✅ 可视化图表已创建\n');
fprintf('   ✅ Visualization created\n');

fprintf('\n=== 步骤2完成 ===\n');
fprintf('=== Step 2 Complete ===\n');
fprintf('准备训练！运行: 步骤3_训练模型_DeepCEST_training_5pool.m\n');
fprintf('Ready for training! Run: 步骤3_训练模型_DeepCEST_training_5pool.m\n');

%% ========================================================================
%% 内置函数：7T标签生成 - Built-in Function: 7T Label Generation
%% ========================================================================

function pTarget_7T = generate_7T_labels_internal(pTarget_3T, ppm_offsets)
% 从3T参数生成7T标签数据 - Generate 7T label data from 3T parameters
%
% 输入：
%   pTarget_3T: [15 × N_voxels] 3T CEST参数
%   ppm_offsets: [N_freq × 1] 频率偏移 (ppm)
% 输出：
%   pTarget_7T: [15 × N_voxels] 7T CEST参数

fprintf('>> 内置7T标签生成模块\n');
fprintf('>> Built-in 7T label generation module\n');

%% 物理参数设置 - Physical Parameters
% 磁场强度
B0_3T = 3.0;        % 3T磁场
B0_7T = 7.0;        % 7T磁场
field_ratio = B0_7T / B0_3T;  % 场强比例 = 2.33

% 质子拉莫尔频率
gamma_H = 42.576;   % MHz/T
freq_3T = gamma_H * B0_3T;  % 127.7 MHz
freq_7T = gamma_H * B0_7T;  % 298.1 MHz

fprintf('   场强比例: %.2f (7T/3T)\n', field_ratio);
fprintf('   Field ratio: %.2f (7T/3T)\n', field_ratio);

%% CEST效应增强模型 - CEST Effect Enhancement Model
% 不同池的场强增强因子 (基于文献)
enhancement_factors = struct();
enhancement_factors.water = 1.0;        % 水池：基本不变
enhancement_factors.mtr = 1.8;          % MTR：中等增强
enhancement_factors.apt = 2.5;          % APT：显著增强
enhancement_factors.noe = 2.2;          % NOE：较强增强
enhancement_factors.cr = 2.0;           % Cr：中等增强

fprintf('   增强因子: 水池=%.1f, MTR=%.1f, APT=%.1f, NOE=%.1f, Cr=%.1f\n', ...
        enhancement_factors.water, enhancement_factors.mtr, enhancement_factors.apt, ...
        enhancement_factors.noe, enhancement_factors.cr);

%% 参数转换 - Parameter Conversion
n_voxels = size(pTarget_3T, 2);
pTarget_7T = zeros(15, n_voxels);

fprintf('   正在转换 %d 个像素的参数...\n', n_voxels);
fprintf('   Converting parameters for %d voxels...\n', n_voxels);

% 振幅参数转换 (参数1-5) - Amplitude Parameters
pTarget_7T(1, :) = pTarget_3T(1, :) * enhancement_factors.water;  % 水池振幅
pTarget_7T(2, :) = pTarget_3T(2, :) * enhancement_factors.mtr;    % MTR振幅
pTarget_7T(3, :) = pTarget_3T(3, :) * enhancement_factors.apt;    % APT振幅
pTarget_7T(4, :) = pTarget_3T(4, :) * enhancement_factors.noe;    % NOE振幅
pTarget_7T(5, :) = pTarget_3T(5, :) * enhancement_factors.cr;     % Cr振幅

% 积分参数转换 (参数6-10) - Integral Parameters
pTarget_7T(6, :) = pTarget_3T(6, :) * enhancement_factors.water;  % 水池积分
pTarget_7T(7, :) = pTarget_3T(7, :) * enhancement_factors.mtr;    % MTR积分
pTarget_7T(8, :) = pTarget_3T(8, :) * enhancement_factors.apt;    % APT积分
pTarget_7T(9, :) = pTarget_3T(9, :) * enhancement_factors.noe;    % NOE积分
pTarget_7T(10, :) = pTarget_3T(10, :) * enhancement_factors.cr;   % Cr积分

% 位置参数转换 (参数11-15) - Position Parameters
% 化学位移按场强比例线性缩放
pTarget_7T(11, :) = pTarget_3T(11, :) * field_ratio;  % 水池位置 (Hz)
pTarget_7T(12, :) = pTarget_3T(12, :) * field_ratio;  % MTR位置 (Hz)
pTarget_7T(13, :) = pTarget_3T(13, :) * field_ratio;  % APT位置 (Hz)
pTarget_7T(14, :) = pTarget_3T(14, :) * field_ratio;  % NOE位置 (Hz)
pTarget_7T(15, :) = pTarget_3T(15, :) * field_ratio;  % Cr位置 (Hz)

%% 添加7T特有的噪声和变异 - Add 7T-specific Noise and Variation
noise_level_7T = 0.02;  % 7T下的额外噪声水平

% 为振幅参数添加适度的随机变异
for i = 1:5
    noise = noise_level_7T * randn(1, n_voxels) .* abs(pTarget_7T(i, :));
    pTarget_7T(i, :) = pTarget_7T(i, :) + noise;
end

% 确保参数在合理范围内
pTarget_7T = max(pTarget_7T, 0);  % 振幅和积分不能为负

%% 质量检查 - Quality Check
% 计算实际增强倍数
enhancement_ratios = zeros(5, 1);
for i = 1:5
    if mean(pTarget_3T(i, :)) > 0
        enhancement_ratios(i) = mean(pTarget_7T(i, :)) / mean(pTarget_3T(i, :));
    end
end

fprintf('   实际增强倍数: 水池=%.2f, MTR=%.2f, APT=%.2f, NOE=%.2f, Cr=%.2f\n', ...
        enhancement_ratios(1), enhancement_ratios(2), enhancement_ratios(3), ...
        enhancement_ratios(4), enhancement_ratios(5));

fprintf('   ✅ 7T标签数据生成完成\n');
fprintf('   ✅ 7T label data generation completed\n');

end
