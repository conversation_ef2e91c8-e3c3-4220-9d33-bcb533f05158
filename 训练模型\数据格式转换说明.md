# 🔄 数据格式转换功能说明

## 📋 **概述**

步骤4推理脚本现在支持**智能数据格式检测和转换**，可以无缝处理两种不同的输入数据格式：

1. **2D向量格式** - 来自您的拟合脚本 `FivePoolsLorentz_FittingTestForSingleDataset.m`
2. **4D图像格式** - 标准CEST数据格式

## 🔍 **支持的数据格式**

### **格式1：2D向量格式（拟合脚本输出）**

```matlab
% 您的拟合脚本生成的MAT文件包含：
zInput: [48 × N_voxels]    % Z谱数据，48个频率点，N_voxels个像素
pTarget: [15 × N_voxels]   % 15个拟合参数，N_voxels个像素
offs: [48 × 1]             % 频率偏移（可选）

% 示例：
% zInput: [48 × 5247] - 48个频率点，5247个有效像素
% pTarget: [15 × 5247] - 15个参数，5247个有效像素
```

**特点：**
- ✅ 来自您现有的拟合工作流程
- ✅ 只包含有效的脑组织像素
- ✅ 数据紧凑，处理效率高
- ⚠️ 缺少空间信息，需要转换用于显示

### **格式2：4D图像格式（标准CEST）**

```matlab
% 标准CEST数据格式：
zSpec: [H × W × S × N_freq]  % Z谱数据，H×W×S为图像尺寸
mask: [H × W × S]            % 脑掩膜，标记有效像素
offs: [N_freq × 1]           % 频率偏移

% 示例：
% zSpec: [96 × 96 × 1 × 73] - 96×96图像，1个切片，73个频率点
% mask: [96 × 96 × 1] - 对应的脑掩膜
```

**特点：**
- ✅ 保持完整的空间信息
- ✅ 标准医学影像格式
- ✅ 直接用于显示和DCM生成
- ⚠️ 包含背景像素，文件较大

## 🔄 **自动转换功能**

### **检测逻辑**

```matlab
% 系统自动检测工作空间中的变量：
if 存在 'zInput' 和 'pTarget'
    → 识别为2D向量格式
    → 调用转换函数转为4D格式
elseif 存在 'zSpec' 和 'mask'
    → 识别为4D图像格式
    → 直接使用，无需转换
else
    → 报错：无法识别数据格式
end
```

### **2D→4D转换算法**

```matlab
% 1. 估算图像尺寸
N_voxels = size(zInput, 2);           % 有效像素数
img_size = ceil(sqrt(N_voxels));      % 估算为方形图像
total_pixels = img_size * img_size;   % 总像素数

% 2. 重塑数据
zSpec_4D = zeros(img_size, img_size, 1, n_freq);
mask_4D = zeros(img_size, img_size, 1);

% 3. 填充数据
for i = 1:N_voxels
    [row, col] = ind2sub([img_size, img_size], i);
    zSpec_4D(row, col, 1, :) = zInput(:, i);
    mask_4D(row, col, 1) = 1;  % 标记为有效像素
end
```

### **转换示例**

```matlab
% 输入：2D向量格式
zInput: [48 × 5247]  % 5247个有效像素
pTarget: [15 × 5247]

% 转换过程：
估算图像尺寸: ceil(sqrt(5247)) = 73
总像素数: 73 × 73 = 5329
有效像素比例: 5247/5329 = 98.5%

% 输出：4D图像格式
zSpec: [73 × 73 × 1 × 48]  % 转换后的Z谱数据
mask: [73 × 73 × 1]        % 生成的脑掩膜
```

## 🎯 **使用方法**

### **对于2D向量数据**

```matlab
% 1. 确保您的MAT文件包含正确的变量名
% 必需变量：
% - zInput: [频率点 × 像素数]
% - pTarget: [15 × 像素数]
% 可选变量：
% - offs 或 pmp_offsets: [频率点 × 1]

% 2. 在步骤4脚本中设置数据文件路径
data_file = 'path/to/your/fitting_results.mat';

% 3. 运行脚本，系统会自动：
% - 检测为2D格式
% - 转换为4D格式
% - 继续推理和显示
```

### **对于4D图像数据**

```matlab
% 1. 确保您的MAT文件包含正确的变量名
% 必需变量：
% - zSpec: [高度 × 宽度 × 切片 × 频率点]
% - mask: [高度 × 宽度 × 切片]
% 可选变量：
% - offs 或 pmp_offsets: [频率点 × 1]

% 2. 运行脚本，系统会自动：
% - 检测为4D格式
% - 直接使用，无需转换
% - 继续推理和显示
```

## 📊 **转换质量控制**

### **数据完整性检查**

```matlab
% 转换过程中的质量控制：
1. 输入数据维度验证
2. 像素数合理性检查（建议 > 1000）
3. 图像尺寸估算验证
4. Z谱数据范围检查（通常0-1之间）
5. 转换后数据完整性验证
```

### **转换报告**

```matlab
% 系统会输出详细的转换信息：
>> 检测数据格式...
✅ 检测到2D向量格式数据 (拟合脚本输出)
数据尺寸: zInput=[48×5247], pTarget=[15×5247]
>> 执行2D→4D格式转换...
估算图像尺寸: 73×73 (总像素: 5329)
有效像素: 5247/5329 (98.5%)
Z谱数据范围: [0.0234, 0.9876]
✅ 2D→4D转换完成
✅ 数据格式转换完成: 2D_converted
```

## ⚠️ **注意事项**

### **转换限制**

1. **图像尺寸估算**：假设像素按方形或接近方形排列
2. **空间信息丢失**：2D数据转换后的空间排列可能与原始图像不完全一致
3. **背景填充**：如果像素数不是完全平方数，会用零填充背景区域

### **最佳实践**

1. **数据质量**：确保输入数据质量良好，无异常值
2. **变量命名**：严格按照要求命名变量（区分大小写）
3. **文件完整性**：确保MAT文件包含所有必需变量
4. **像素数量**：建议至少1000个有效像素以获得合理的图像尺寸

## 🔧 **故障排除**

### **常见错误1：无法识别数据格式**
```
❌ 无法识别数据格式。需要 (zInput+pTarget) 或 (zSpec+mask)
```
**解决方案：**
- 检查变量名拼写（区分大小写）
- 确认MAT文件包含必需的变量组合

### **常见错误2：数据维度不匹配**
```
❌ zInput和pTarget的像素数不一致
```
**解决方案：**
- 检查拟合脚本的输出
- 确认两个变量的第二维度相同

### **常见错误3：转换后图像异常**
```
⚠️ 转换后的图像尺寸过大或过小
```
**解决方案：**
- 检查输入像素数是否合理
- 考虑手动指定图像尺寸（修改转换函数）

## 🎯 **总结**

这个数据格式转换功能让您可以：

- ✅ **无缝兼容**：支持您现有的拟合工作流程
- ✅ **自动处理**：无需手动修改数据格式
- ✅ **智能转换**：自动检测并转换为合适的格式
- ✅ **质量保证**：完整的转换质量控制和报告
- ✅ **灵活支持**：同时支持2D和4D两种格式

**现在您可以直接使用拟合脚本的输出进行3T→7T跨场强预测了！** 🚀🔄
