%% 步骤1：整理拟合数据 - 保持原始患者ID和检查时间结构
% Step 1: Organize Fitted Data - Preserve Original Patient ID and Session Structure
% 
% 功能说明：
% 1. 从拟合结果文件夹复制MAT文件到训练模型文件夹
% 2. 保持原始的患者ID/检查时间文件夹结构
% 3. 验证MAT文件包含训练所需的变量
%
% 使用方法：
% cd('C:\Users\<USER>\Documents\augment-projects\1\训练模型')
% run('步骤1_整理拟合数据_organize_fitted_data.m')

clear all; close all; clc; warning off;

fprintf('=== 步骤1：整理拟合数据，保持原始结构 ===\n');
fprintf('=== Step 1: Organize Fitted Data with Original Structure ===\n\n');

%% 第1部分：配置路径 - Configuration
fprintf('>> 第1部分：配置文件路径\n');

% 源路径：您的拟合结果存放位置
% Source: where your fitting results are stored
source_base = 'C:\Users\<USER>\Documents\augment-projects\A4CestParam5Pools_48points_APT_CEST_15';

% 目标路径：训练模型数据文件夹
% Target: training model data folder
target_base = 'Data';  % 这将创建：训练模型/Data/

% 创建目标文件夹（如果不存在）
% Create target directory if it doesn't exist
if ~exist(target_base, 'dir')
    mkdir(target_base);
    fprintf('   ✅ 已创建目标文件夹: %s\n', target_base);
    fprintf('   ✅ Created target directory: %s\n', target_base);
end

%% 第2部分：查找拟合数据 - Find Fitted Data
fprintf('\n>> 第2部分：查找所有拟合数据文件\n');

% 检查源文件夹是否存在
% Check if source directory exists
if ~exist(source_base, 'dir')
    error('❌ 源文件夹不存在: %s\n请先运行您的拟合脚本！\nSource directory not found: %s\nPlease run your fitting script first!', source_base);
end

% 查找所有患者文件夹
% Find all patient directories
patient_dirs = dir(source_base);
patient_dirs = patient_dirs([patient_dirs.isdir] & ~ismember({patient_dirs.name}, {'.', '..'}));

fprintf('   在源文件夹中找到 %d 个患者文件夹\n', length(patient_dirs));
fprintf('   Found %d patient directories in source\n', length(patient_dirs));

copied_count = 0;           % 成功复制的文件计数 - Count of successfully copied files
patient_info = struct();    % 患者信息存储 - Patient information storage

%% 第3部分：处理每个患者 - Process Each Patient
fprintf('\n>> 第3部分：逐个处理患者数据\n');

for p = 1:length(patient_dirs)
    patient_id = patient_dirs(p).name;  % 患者ID - Patient ID
    patient_source_dir = fullfile(source_base, patient_id);
    
    fprintf('\n--- 正在处理患者: %s ---\n', patient_id);
    fprintf('--- Processing Patient: %s ---\n', patient_id);
    
    % 查找该患者的检查时间文件夹
    % Find session directories within this patient
    session_dirs = dir(patient_source_dir);
    session_dirs = session_dirs([session_dirs.isdir] & ~ismember({session_dirs.name}, {'.', '..'}));
    
    if isempty(session_dirs)
        fprintf('   ⚠️  患者 %s 没有找到检查时间文件夹\n', patient_id);
        fprintf('   ⚠️  No session directories found for patient %s\n', patient_id);
        continue;
    end
    
    % 处理每个检查时间
    % Process each session
    for s = 1:length(session_dirs)
        session_id = session_dirs(s).name;  % 检查时间ID - Session ID
        session_source_dir = fullfile(patient_source_dir, session_id);
        
        fprintf('  检查时间: %s\n', session_id);
        fprintf('  Session: %s\n', session_id);
        
        % 查找该检查时间的MAT文件
        % Find MAT files in this session
        mat_files = dir(fullfile(session_source_dir, '*.mat'));
        
        if isempty(mat_files)
            fprintf('    ❌ 在 %s 中没有找到MAT文件\n', session_source_dir);
            fprintf('    ❌ No MAT files found in %s\n', session_source_dir);
            continue;
        end
        
        % 创建目标文件夹结构
        % Create target directory structure
        target_patient_dir = fullfile(target_base, patient_id, session_id);
        if ~exist(target_patient_dir, 'dir')
            mkdir(target_patient_dir);
            fprintf('    ✅ 已创建: %s\n', target_patient_dir);
            fprintf('    ✅ Created: %s\n', target_patient_dir);
        end
        
        % 复制每个MAT文件
        % Copy each MAT file
        for m = 1:length(mat_files)
            source_file = fullfile(session_source_dir, mat_files(m).name);
            target_file = fullfile(target_patient_dir, mat_files(m).name);
            
            try
                % 加载并验证MAT文件
                % Load and verify the MAT file
                fprintf('    正在检查: %s\n', mat_files(m).name);
                fprintf('    Checking: %s\n', mat_files(m).name);
                data = load(source_file);
                
                % 验证是否包含训练所需的变量
                % Verify it contains the expected variables
                if isfield(data, 'zInput') && isfield(data, 'pTarget')
                    fprintf('      ✅ 有效的训练数据: zInput %s, pTarget %s\n', ...
                            mat2str(size(data.zInput)), mat2str(size(data.pTarget)));
                    fprintf('      ✅ Valid training data: zInput %s, pTarget %s\n', ...
                            mat2str(size(data.zInput)), mat2str(size(data.pTarget)));
                    
                    % 复制文件
                    % Copy the file
                    copyfile(source_file, target_file);
                    fprintf('      ✅ 已复制到: %s\n', target_file);
                    fprintf('      ✅ Copied to: %s\n', target_file);
                    
                    % 存储信息
                    % Store information
                    copied_count = copied_count + 1;
                    patient_info(copied_count).patient_id = patient_id;      % 患者ID
                    patient_info(copied_count).session_id = session_id;      % 检查时间ID
                    patient_info(copied_count).filename = mat_files(m).name; % 文件名
                    patient_info(copied_count).source_path = source_file;    % 源路径
                    patient_info(copied_count).target_path = target_file;    % 目标路径
                    patient_info(copied_count).n_voxels = size(data.zInput, 2);   % 像素数量
                    patient_info(copied_count).n_params = size(data.pTarget, 1);  % 参数数量
                    patient_info(copied_count).n_freq = size(data.zInput, 1);     % 频率点数
                    
                else
                    fprintf('      ❌ 无效的MAT文件 (缺少 zInput 或 pTarget)\n');
                    fprintf('      ❌ Invalid MAT file (missing zInput or pTarget)\n');
                    fprintf('      可用变量: %s\n', strjoin(fieldnames(data), ', '));
                    fprintf('      Available variables: %s\n', strjoin(fieldnames(data), ', '));
                end
                
            catch ME
                fprintf('      ❌ 处理 %s 时出错: %s\n', mat_files(m).name, ME.message);
                fprintf('      ❌ Error processing %s: %s\n', mat_files(m).name, ME.message);
            end
        end
    end
end

%% 第4部分：生成总结报告 - Create Summary
fprintf('\n=== 第4部分：处理结果总结 ===\n');
fprintf('=== Part 4: Processing Summary ===\n');

fprintf('成功复制了 %d 个MAT文件\n', copied_count);
fprintf('Successfully copied %d MAT files\n', copied_count);

if copied_count > 0
    fprintf('\n已复制的文件:\n');
    fprintf('Copied files:\n');
    for i = 1:copied_count
        fprintf('  %s/%s: %s (%d 像素, %d 参数)\n', ...
                patient_info(i).patient_id, patient_info(i).session_id, ...
                patient_info(i).filename, patient_info(i).n_voxels, patient_info(i).n_params);
    end
    
    % 保存组织信息
    % Save the organization info
    save(fullfile(target_base, 'data_organization_info.mat'), 'patient_info', 'copied_count');
    fprintf('\n数据组织信息已保存到: %s\n', fullfile(target_base, 'data_organization_info.mat'));
    fprintf('Data organization info saved to: %s\n', fullfile(target_base, 'data_organization_info.mat'));
end

%% 第5部分：显示新的文件结构 - Display New Structure
fprintf('\n=== 第5部分：新的数据结构 ===\n');
fprintf('=== Part 5: New Data Structure ===\n');
fprintf('训练模型/Data/\n');

if copied_count > 0
    % 按患者分组显示
    % Group by patient
    unique_patients = unique({patient_info.patient_id});
    
    for p = 1:length(unique_patients)
        patient_id = unique_patients{p};
        fprintf('├── %s/  (患者ID)\n', patient_id);
        
        % 查找该患者的检查时间
        % Find sessions for this patient
        patient_sessions = {patient_info(strcmp({patient_info.patient_id}, patient_id)).session_id};
        unique_sessions = unique(patient_sessions);
        
        for s = 1:length(unique_sessions)
            session_id = unique_sessions{s};
            if s == length(unique_sessions)
                fprintf('│   └── %s/  (检查时间)\n', session_id);
            else
                fprintf('│   ├── %s/  (检查时间)\n', session_id);
            end
            
            % 查找该患者/检查时间的文件
            % Find files for this patient/session
            session_files = {patient_info(strcmp({patient_info.patient_id}, patient_id) & ...
                                        strcmp({patient_info.session_id}, session_id)).filename};
            
            for f = 1:length(session_files)
                if s == length(unique_sessions) && f == length(session_files)
                    fprintf('│       └── %s  (拟合数据)\n', session_files{f});
                elseif s == length(unique_sessions)
                    fprintf('│       ├── %s  (拟合数据)\n', session_files{f});
                else
                    fprintf('│   │   ├── %s  (拟合数据)\n', session_files{f});
                end
            end
        end
    end
end

%% 第6部分：下一步指导 - Next Steps Guidance
fprintf('\n=== 第6部分：下一步操作指导 ===\n');
fprintf('=== Part 6: Next Steps Guidance ===\n');

if copied_count > 0
    fprintf('✅ 数据整理完成！\n');
    fprintf('✅ Data organization complete!\n\n');
    
    fprintf('您的拟合数据现在按以下结构组织:\n');
    fprintf('Your fitted data is now organized as:\n');
    fprintf('训练模型/Data/患者ID/检查时间ID/拟合数据.mat\n');
    fprintf('训练模型/Data/PatientID/SessionID/FittedData.mat\n\n');
    
    fprintf('下一步操作:\n');
    fprintf('Next steps:\n');
    fprintf('1. 运行批量处理脚本合并所有患者数据:\n');
    fprintf('1. Run batch processing to combine all patients:\n');
    fprintf('   >> run(''步骤2_批量处理数据_batch_process_organized_data.m'')\n\n');
    
    fprintf('2. 然后训练模型:\n');
    fprintf('2. Then train the model:\n');
    fprintf('   >> run(''步骤3_训练模型_DeepCEST_training_5pool.m'')\n\n');
    
else
    fprintf('❌ 没有找到有效的拟合数据！\n');
    fprintf('❌ No valid fitted data found!\n\n');
    
    fprintf('请检查:\n');
    fprintf('Please check:\n');
    fprintf('1. 您是否已经运行了拟合脚本?\n');
    fprintf('1. Have you run your fitting script?\n');
    fprintf('2. MAT文件是否在预期位置?\n');
    fprintf('2. Are the MAT files in the expected location?\n');
    fprintf('3. MAT文件是否包含 zInput 和 pTarget 变量?\n');
    fprintf('3. Do the MAT files contain zInput and pTarget variables?\n\n');
    
    fprintf('预期的源文件位置:\n');
    fprintf('Expected source location:\n');
    fprintf('%s/患者ID/检查时间ID/*.mat\n', source_base);
    fprintf('%s/PatientID/SessionID/*.mat\n', source_base);
end

fprintf('\n=== 步骤1完成 ===\n');
fprintf('=== Step 1 Complete ===\n');
